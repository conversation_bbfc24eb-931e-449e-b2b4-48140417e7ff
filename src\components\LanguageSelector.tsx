import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"

// 支持的语言列表
const SUPPORTED_LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'zh_CN', label: '简体中文' },
  { value: 'zh_TW', label: '繁體中文' },
  { value: 'hi', label: 'हिन्दी' },
  { value: 'es', label: 'Español' },
  { value: 'fr', label: 'Français' },
  { value: 'ru', label: 'Русский' },
  { value: 'id', label: 'Bahasa Indonesia' },
  { value: 'bn', label: 'বাংলা' },
  { value: 'pt', label: 'Português' },
  { value: 'de', label: 'Deutsch' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'vi', label: 'Tiếng Việt' },
  { value: 'tr', label: 'Türkçe' },
  { value: 'it', label: 'Italiano' }
]

interface LanguageSelectorProps {
  className?: string
}

export default function LanguageSelector({ className = "" }: LanguageSelectorProps) {
  const { i18n } = useTranslation()
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language)

  useEffect(() => {
    setCurrentLanguage(i18n.language)
  }, [i18n.language])

  const handleLanguageChange = async (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = event.target.value
    try {
      await i18n.changeLanguage(newLanguage)
      setCurrentLanguage(newLanguage)
      
      // 保存用户选择的语言到本地存储
      await chrome.storage.local.set({ 'snapany_user_language': newLanguage })
      console.log('语言已切换到:', newLanguage)
    } catch (error) {
      console.error('切换语言失败:', error)
    }
  }

  return (
    <div className={`language-selector ${className}`}>
      <select
        value={currentLanguage}
        onChange={handleLanguageChange}
        className="px-2 py-1 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        {SUPPORTED_LANGUAGES.map((lang) => (
          <option key={lang.value} value={lang.value}>
            {lang.label}
          </option>
        ))}
      </select>
    </div>
  )
}
