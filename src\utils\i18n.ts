import { getBackgroundText } from '../i18n'

/**
 * 在background script中使用的多语言工具函数
 * @param key 翻译键，支持嵌套键如 'common.title'
 * @returns 翻译后的文本
 */
export function t(key: string): string {
  return getBackgroundText(key)
}

/**
 * 获取当前语言代码
 * @returns 语言代码，如 'en', 'zh_CN'
 */
export function getCurrentLanguage(): string {
  try {
    const chromeLanguage = chrome?.i18n?.getUILanguage?.()
    if (chromeLanguage) {
      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')
      if (normalizedLang.startsWith('zh')) {
        return 'zh_CN'
      }
      return normalizedLang.split('_')[0] || 'en'
    }
  } catch (error) {
    console.log('Chrome i18n API不可用，使用navigator.language')
  }

  const browserLanguage = navigator.language || 'en'
  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')
  
  if (normalizedBrowserLang.startsWith('zh')) {
    return 'zh_CN'
  }
  
  return normalizedBrowserLang.split('_')[0] || 'en'
}

/**
 * 检查是否为中文环境
 * @returns 是否为中文
 */
export function isChinese(): boolean {
  return getCurrentLanguage() === 'zh_CN'
}
