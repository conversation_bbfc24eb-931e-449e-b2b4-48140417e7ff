import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

import en from './locales/en.json'
import zh_CN from './locales/zh_CN.json'

// 语言映射表
const languageMap: Record<string, any> = {
  'en': en,
  'zh': zh_CN,
  'zh-CN': zh_CN,
  'zh_CN': zh_CN
}

// 为 background script 提供的简单多语言函数
export function getBackgroundText(key: string): string {
  const currentLanguage = chrome.i18n.getUILanguage()

  // 尝试精确匹配
  let translations = languageMap[currentLanguage]

  // 如果没有精确匹配，尝试语言前缀匹配
  if (!translations) {
    const languagePrefix = currentLanguage.split('-')[0]
    translations = languageMap[languagePrefix]
  }

  // 如果还是没有，回退到英语
  if (!translations) {
    translations = en
  }

  // 支持嵌套键，如 'common.title'
  const keys = key.split('.')
  let result: any = translations

  for (const k of keys) {
    result = result?.[k]
    if (result === undefined) break
  }

  return result || key
}

const resources = {
  en: { translation: en },
  zh_CN: { translation: zh_CN }
}

// 检测浏览器语言
function detectLanguage(): string {
  try {
    const chromeLanguage = chrome?.i18n?.getUILanguage?.()
    if (chromeLanguage) {
      // 标准化语言代码
      const normalizedLang = chromeLanguage.toLowerCase().replace('-', '_')

      // 检查是否支持该语言
      if (resources[normalizedLang]) {
        return normalizedLang
      }

      // 尝试语言前缀匹配
      const languagePrefix = normalizedLang.split('_')[0]
      if (languagePrefix === 'zh') {
        return 'zh_CN' // 默认使用简体中文
      }
      if (resources[languagePrefix]) {
        return languagePrefix
      }
    }
  } catch (error) {
    console.log('Chrome i18n API不可用，使用navigator.language')
  }

  const browserLanguage = navigator.language || navigator.languages?.[0] || 'en'
  const normalizedBrowserLang = browserLanguage.toLowerCase().replace('-', '_')

  // 检查是否支持该语言
  if (resources[normalizedBrowserLang]) {
    return normalizedBrowserLang
  }

  // 尝试语言前缀匹配
  const languagePrefix = normalizedBrowserLang.split('_')[0]
  if (languagePrefix === 'zh') {
    return 'zh_CN' // 默认使用简体中文
  }
  if (resources[languagePrefix]) {
    return languagePrefix
  }

  // 回退到英语
  return 'en'
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: detectLanguage(),
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false
    }
  })

export default i18n